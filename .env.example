# Email Configuration for Contact Form
# Copy this file to .env and fill in your email credentials

# SMTP Server Settings
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587

# Your email credentials
# For Gmail, you'll need to use an "App Password" instead of your regular password
# See: https://support.google.com/accounts/answer/185833
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password

# From address (can be the same as EMAIL_USERNAME)
EMAIL_FROM=<EMAIL>

# Admin credentials (optional - change from defaults)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-password

# Instructions:
# 1. Copy this file to .env: cp .env.example .env
# 2. Fill in your actual email credentials
# 3. For Gmail users:
#    - Enable 2-factor authentication
#    - Generate an App Password: https://myaccount.google.com/apppasswords
#    - Use the App Password in EMAIL_PASSWORD field
# 4. For other email providers, update EMAIL_HOST and EMAIL_PORT accordingly
#
# Common SMTP settings:
# Gmail: smtp.gmail.com:587
# Outlook: smtp-mail.outlook.com:587
# Yahoo: smtp.mail.yahoo.com:587
# ProtonMail: smtp.protonmail.com:587 (requires ProtonMail Bridge)
