ADMIN CREDENTIALS SETUP
=======================

For security, you should change the default admin credentials.

Current defaults:
- Username: admin
- Password: barbarossa2024

To change these, you can:

1. Set environment variables:
   export ADMIN_USERNAME=your_username
   export ADMIN_PASSWORD=your_secure_password

2. Or modify the main.py file directly:
   ADMIN_USERNAME = "your_username"
   ADMIN_PASSWORD = "your_secure_password"

SECURITY RECOMMENDATIONS:
- Use a strong password (12+ characters, mix of letters, numbers, symbols)
- Change the default credentials immediately
- Use environment variables in production
- Enable HTTPS in production and set secure=True in cookies
- Consider using a proper database for session storage in production

LOGIN URL: http://localhost:8000/admin/login 