{% extends "base.html" %}

{% block title %}Home - Your Business{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section d-flex align-items-center py-5" style="background: #653119;">
    
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold text-white mb-4">
                    Welcome to <span class="text-warning">Barbarossa Lives Game Studio</span>
                </h1>
                <p class="lead text-white-50 mb-4">
                    Code, Create, Endure.
                </p>
                <div class="d-flex flex-column align-items-start">
                    <div class="d-flex gap-3 mb-3">
                        <a href="/contact" class="btn btn-warning btn-lg px-4">
                            <i class="fas fa-phone me-2"></i>Contact Us
                        </a>
                        <a href="/about" class="btn btn-outline-light btn-lg px-4">
                            <i class="fas fa-info-circle me-2"></i>Learn more about us
                        </a>
                    </div>
                    
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <div class="hero-image">
                    <img src="{{ url_for('static', path='/images/logoTransparentNoWords.png') }}" alt="Barbarossa Lives Logo" style="width: 600px; height: 600px; object-fit: contain; opacity: 0.9;">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Blog Section -->
<section class="py-5 bg-dark">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-lg-8 mx-auto">
                <h2 class="display-5 fw-bold mb-3 text-white">Latest Blog Posts</h2>
                <p class="lead text-white">
                    Insights, tutorials, and updates from our development journey
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            {% if blog_posts %}
                {% for post in blog_posts %}
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="position-relative">
                            <a href="/blog/{{ post.id }}">
                                <img src="{{ url_for('static', path='/images/' + post.image) }}"
                                     class="card-img-top"
                                     alt="{{ post.title }}"
                                     style="height: 200px; object-fit: cover;">
                            </a>
                            <div class="position-absolute top-0 start-0 m-3">
                                <span class="badge bg-primary">{{ post.category }}</span>
                            </div>
                        </div>
                        <div class="card-body p-4 d-flex flex-column">
                            <div class="mb-3">
                                <small class="text-muted">
                                    <i class="fas fa-calendar-alt me-1"></i>{{ post.date }}
                                    <span class="mx-2">•</span>
                                    <i class="fas fa-clock me-1"></i>{{ post.read_time }}
                                </small>
                            </div>
                            <h5 class="card-title mb-3">
                                <a href="/blog/{{ post.id }}" class="text-decoration-none text-dark">{{ post.title }}</a>
                            </h5>
                            <p class="card-text text-muted flex-grow-1">{{ post.excerpt }}</p>
                            <div class="mt-auto d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-user me-1"></i>{{ post.author }}
                                </small>
                                <a href="/blog/{{ post.id }}" class="btn btn-outline-primary btn-sm">Read More</a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="col-12 text-center">
                    <p class="text-white">No blog posts found.</p>
                </div>
            {% endif %}
        </div>
        
        <div class="row mt-4">
            <div class="col-12 text-center">
                <a href="/blog" class="btn btn-primary btn-lg px-4">
                    <i class="fas fa-blog me-2"></i>View All Posts
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="py-5" style="background: #58E5F7;">
    <div class="container">
        <div class="row text-center mb-5 pt-20">
            <div class="col-lg-8 mx-auto">
                <h2 class="display-5 fw-bold mb-3 text-dark">Freelance and Custom Software</h2>
                <p class="lead text-dark">
                    We offer custom software solutions tailored to meet your specific needs and the you work.
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm" style="background: black;">
                    <div class="card-body text-center p-4">
                        <div class="service-icon mb-3">
                            <i class="fa-solid fa-square-share-nodes" style="font-size: 5rem; color: white;"></i>
                        </div>
                        <h3 class="card-title" style="color: white;">Custom Social Media Posting</h3>
                        <p class="card-text" style="color: white;">
                            I can create a custom program that helps to post to many social media platforms with the click of a button. **
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm" style="background: black;">
                    <div class="card-body text-center p-4">
                        <div class="service-icon mb-3">
                            <i class="fa-solid fa-file-invoice-dollar" style="font-size: 5rem; color: white;"></i>
                        </div>
                        <h3 class="card-title" style="color: white;">Custom Finacial Tools</h3>
                        <p class="card-text" style="color: white;">
                            I can create tools that helps keep track of finaces, expenses and imvoicing in a way that works for you and your business.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm" style="background: black;">
                    <div class="card-body text-center p-4">
                        <div class="service-icon mb-3">
                            <i class="fa-solid fa-list-check" style="font-size: 5rem; color: white;"></i>
                        </div>
                        <h3 class="card-title" style="color: white;">Personalized Project Tracker</h3>
                        <p class="card-text" style="color: white;">
                            I can create a custom solution for Project Management. Including how you create projects and tasks, how you see, edit and review them, all wokinig in a way that works for you.
                        </p>
                    </div>
                </div>
            </div>
            <p class="text-black"> ** Some social media platforms do not allow posting without a paid account</p>
        </div>
    </div>
</section>

<!-- Games Section -->
<section class="py-5" style="background: #653119;">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-lg-8 mx-auto">
                <h2 class="display-5 fw-bold mb-3 text-white">Game Development</h2>
                <p class="lead text-white">
                    Creating game that I want to play. Expolring how to blur the lines computer games and TTRPG's. I create tools for game masters, player and other developers.  
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm" style="background: black;">
                    <div class="card-body text-center p-4">
                        <div class="mb-3">
                            <img src="{{ url_for('static', path='/images/CryptoSim.png') }}" alt="Crypto Currency Simulator" style="width: 320px; height: 180px; object-fit: contain;">
                        </div>
                        <h3 class="card-title" style="color: white;">Crypto Currency Simulator</h3>
                        <p class="card-text" style="color: white;">
                            Ai powered crypto currency simulator that allows you to trade and invest in crypto currency. It also has several seenarios to win the game.</p>
                            <a href="https://github.com/BarbarossaLives/CryptoCurrencySim" target="_blank">GIT HUB REPO</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm" style="background: black;">
                    <div class="card-body text-center p-4">
                        <div class="mb-3">
                            <img src="{{ url_for('static', path='/images/DiceRoller.png') }}" alt="Crypto Currency Simulator" style="width: 320px; height: 180px; object-fit: contain;">
                        </div>
                        <h3 class="card-title" style="color: white;">Role Playing Dice Roller</h3>
                        <p class="card-text" style="color: white;">
                            I created a dice roller capable of working for many TTRPG games. It allows you to choose the amount and type of dice as well as modifiers. It shows the result of each die and the total.    
                        </p>
                        <a href="https://github.com/BarbarossaLives/DiceRollerTool" target="_blank">GIT HUB REPO</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm" style="background: black;">
                    <div class="card-body text-center p-4">
                        <div class="mb-3">
                            <img src="{{ url_for('static', path='/images/RuleBeyton_1.png') }}" alt="Crypto Currency Simulator" style="width: 320px; height: 180px; object-fit: contain;">
                        </div>
                        <h3 class="card-title" style="color: white;">Rule Beyton</h3>
                        <p class="card-text" style="color: white;">
                            Currently developing a 3d RPG game involing a working your way up the ladder of the local thieve's guild to become the master. </p>
                            <a href="">Project Page</a>
                        
                    </div>
                </div>
            </div>
            
        </div>
        <div class="text-center mt-5">
            <h3><a href="">All of our Projects</a></h3>
        </div>
    </div>
</section>

<!-- Image Carousel Section -->
<section class="py-5 bg-dark">
    <div class="container">
        <div class="row text-center mb-4">
            <div class="col-lg-8 mx-auto">
                <h2 class="display-6 fw-bold mb-3 text-white">Gallery</h2>
                <p class="lead text-light mb-4">
                    Visual insight to the software and games we are working on.
            </div>
        </div>
        
        <div id="projectCarousel" class="carousel slide" data-bs-ride="carousel">
            <!-- Carousel Indicators -->
            <div class="carousel-indicators">
                <button type="button" data-bs-target="#projectCarousel" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
                <button type="button" data-bs-target="#projectCarousel" data-bs-slide-to="1" aria-label="Slide 2"></button>
                <button type="button" data-bs-target="#projectCarousel" data-bs-slide-to="2" aria-label="Slide 3"></button>
                <button type="button" data-bs-target="#projectCarousel" data-bs-slide-to="3" aria-label="Slide 4"></button>
                <button type="button" data-bs-target="#projectCarousel" data-bs-slide-to="4" aria-label="Slide 5"></button>
                <button type="button" data-bs-target="#projectCarousel" data-bs-slide-to="5" aria-label="Slide 6"></button>
                <button type="button" data-bs-target="#projectCarousel" data-bs-slide-to="6" aria-label="Slide 7"></button>
                <button type="button" data-bs-target="#projectCarousel" data-bs-slide-to="7" aria-label="Slide 8"></button>
            </div>
            
            <!-- Carousel Items -->
            <div class="carousel-inner">
                <div class="carousel-item active">
                    <img src="{{ url_for('static', path='/images/RuleBeyton_1.png') }}" class="d-block w-100" alt="Rule Beyton Game" style="max-height: 500px; object-fit: contain;">
                    <div class="carousel-caption d-none d-md-block">
                        <h5>Rule Beyton</h5>
                        <p>3D RPG Game Development</p>
                    </div>
                </div>
                <div class="carousel-item">
                    <img src="{{ url_for('static', path='/images/DiceRoller.png') }}" class="d-block w-100" alt="Dice Roller App" style="max-height: 500px; object-fit: contain;">
                    <div class="carousel-caption d-none d-md-block">
                        <h5>Dice Roller</h5>
                        <p>TTRPG Dice Rolling Application</p>
                    </div>
                </div>
                <div class="carousel-item">
                    <img src="{{ url_for('static', path='/images/CryptoSim.png') }}" class="d-block w-100" alt="Crypto Simulator" style="max-height: 500px; object-fit: contain;">
                    <div class="carousel-caption d-none d-md-block">
                        <h5>Crypto Simulator</h5>
                        <p>AI-Powered Cryptocurrency Trading Simulator</p>
                    </div>
                </div>
                <div class="carousel-item">
                    <img src="{{ url_for('static', path='/images/game_tool_full.png') }}" class="d-block w-100" alt="Game Development Tools" style="max-height: 500px; object-fit: contain;">
                    <div class="carousel-caption d-none d-md-block">
                        <h5>Game Development Tools</h5>
                        <p>Comprehensive Game Development Suite</p>
                    </div>
                </div>
                <div class="carousel-item">
                    <img src="{{ url_for('static', path='/images/CrossPostingApp.png') }}" class="d-block w-100" alt="Cross Posting App" style="max-height: 500px; object-fit: contain;">
                    <div class="carousel-caption d-none d-md-block">
                        <h5>Cross Posting App</h5>
                        <p>Social Media Management Tool</p>
                    </div>
                </div>
                <div class="carousel-item">
                    <img src="{{ url_for('static', path='/images/checkbook.png') }}" class="d-block w-100" alt="Financial Tools" style="max-height: 500px; object-fit: contain;">
                    <div class="carousel-caption d-none d-md-block">
                        <h5>Financial Management</h5>
                        <p>Custom Financial Tracking Tools</p>
                    </div>
                </div>
                <div class="carousel-item">
                    <img src="{{ url_for('static', path='/images/crypto_board.png') }}" class="d-block w-100" alt="Crypto Board" style="max-height: 500px; object-fit: contain;">
                    <div class="carousel-caption d-none d-md-block">
                        <h5>Crypto Trading Board</h5>
                        <p>Advanced Trading Interface</p>
                    </div>
                </div>
                <div class="carousel-item">
                    <img src="{{ url_for('static', path='/images/crypto_optons.png') }}" class="d-block w-100" alt="Crypto Options" style="max-height: 500px; object-fit: contain;">
                    <div class="carousel-caption d-none d-md-block">
                        <h5>Crypto Options</h5>
                        <p>Options Trading Simulation</p>
                    </div>
                </div>
            </div>
            
            <!-- Carousel Controls -->
            <button class="carousel-control-prev" type="button" data-bs-target="#projectCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Previous</span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#projectCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Next</span>
            </button>
        </div>
    </div>
</section>
{% endblock %} 