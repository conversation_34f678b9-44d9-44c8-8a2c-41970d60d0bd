<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Business Website{% endblock %}</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="/favicon.ico">
    
    <!-- Bootstrap Slate Theme CSS -->
    <link rel="stylesheet" href="{{ url_for('static', path='/css/bootstrap.min.css') }}">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', path='/css/style.css') }}">

    <link rel="apple-touch-icon" sizes="180x180" href="images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="images/favicon-16x16.png">
    <link rel="manifest" href="images/site.webmanifest">
    <link rel="mask-icon" href="images/safari-pinned-tab.svg" color="#5bbad5">
    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="theme-color" content="#ffffff">
    
    <style>
        body {
            padding-top: 76px !important;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container d-flex align-items-center">
            <!-- Dark mode toggle icon -->
            <button id="darkModeToggle" class="btn btn-link p-0 me-3" aria-label="Toggle dark mode" style="font-size: 1.5rem;">
                <i id="darkModeIcon" class="fas fa-moon text-light"></i>
            </button>
            <a class="navbar-brand fw-bold" href="/">
                <img src="{{ url_for('static', path='/images/logoTransparentNoWords.png') }}" alt="Barbarossa Lives Logo" height="40" class="me-2">
                Barbarossa Lives Game Studio
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/games">Games</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/business-solutions">Business Solutions</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/blog">Blog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/about">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/contact">Contact</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-warning" href="/admin/login">
                            <i class="fas fa-cog me-1"></i>Admin
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>Barbarossa Lives Game Studio</h5>
                    <p class="mb-0">Code, Create, Endure.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="social-links">
                        <a href="https://ko-fi.com/barbarossalivesgamestudio" " class="text-light me-3">Ko_fi</a>
                        <a href="style="background: #653119;"" class="text-light me-3">Bluesky</a>
                        <a href="https://rumble.com/user/BarbarossaLives" class="text-light me-3">Rumble</a>
                        <a href="https://www.youtube.com/@BarbarossaLives" class="text-light me-3">YouTube</a>
                        <a href="https://www.fiverr.com/users/montedre/portfolio"background: #653119;"" class="text-light me-3">Fiverr</a>
                        
                    </div>
                    <p class="mt-2 mb-0">&copy; 2024 Barbarossa Lives Game Studio. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', path='/js/main.js') }}"></script>
    <script>
        // Dark mode toggle logic
        const darkModeToggle = document.getElementById('darkModeToggle');
        const darkModeIcon = document.getElementById('darkModeIcon');
        function setDarkMode(enabled) {
            if (enabled) {
                document.body.classList.add('dark-mode');
                darkModeIcon.classList.remove('fa-moon');
                darkModeIcon.classList.add('fa-sun');
            } else {
                document.body.classList.remove('dark-mode');
                darkModeIcon.classList.remove('fa-sun');
                darkModeIcon.classList.add('fa-moon');
            }
        }
        // Load preference
        const darkPref = localStorage.getItem('darkMode') === 'true';
        setDarkMode(darkPref);
        darkModeToggle.addEventListener('click', () => {
            const enabled = !document.body.classList.contains('dark-mode');
            setDarkMode(enabled);
            localStorage.setItem('darkMode', enabled);
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html> 