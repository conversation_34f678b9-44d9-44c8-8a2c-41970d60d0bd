{% extends "base.html" %}

{% block title %}Blog - Barbarossa Lives Game Studio{% endblock %}

{% block content %}
<!-- Blog Hero Section -->
<section class="py-5" style="background: linear-gradient(135deg, #653119 0%, #8B4513 100%);">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold text-white mb-4">Game Development Blog</h1>
                <p class="lead text-white-50 mb-0">Insights, tutorials, and thoughts on game development, indie gaming, and the industry.</p>
            </div>
        </div>
    </div>
</section>

<!-- Blog Posts Section -->
<section class="py-5">
    <div class="container">
        <!-- Blog Header -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="h3 mb-0">Latest Posts</h2>
                    <div class="d-flex gap-2">
                        <select class="form-select form-select-sm" style="width: auto;">
                            <option>All Categories</option>
                            <option>Game Development</option>
                            <option>Industry Insights</option>
                            <option>Development</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Blog Posts Grid -->
        <div class="row g-4">
            {% for post in blog_posts %}
            <div class="col-lg-4 col-md-6">
                <article class="card h-100 shadow-sm border-0">
                    <!-- Post Image -->
                    <div class="position-relative">
                        <a href="/blog/{{ post.id }}">
                            <img src="{{ url_for('static', path='/images/' + post.image) }}" 
                                 class="card-img-top" 
                                 alt="{{ post.title }}"
                                 style="height: 200px; object-fit: cover;">
                        </a>
                        <div class="position-absolute top-0 start-0 m-3">
                            <span class="badge bg-primary">{{ post.category }}</span>
                        </div>
                    </div>
                    
                    <!-- Post Content -->
                    <div class="card-body d-flex flex-column">
                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="fas fa-calendar-alt me-1"></i>{{ post.date }}
                                <span class="mx-2">•</span>
                                <i class="fas fa-clock me-1"></i>{{ post.read_time }}
                            </small>
                        </div>
                        
                        <h5 class="card-title mb-3">
                            <a href="/blog/{{ post.id }}" class="text-decoration-none text-dark">{{ post.title }}</a>
                        </h5>
                        <p class="card-text text-muted flex-grow-1">{{ post.excerpt }}</p>
                        
                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-user me-1"></i>{{ post.author }}
                                </small>
                                <a href="/blog/{{ post.id }}" class="btn btn-outline-primary btn-sm">Read More</a>
                            </div>
                        </div>
                    </div>
                </article>
            </div>
            {% endfor %}
        </div>

        <!-- Load More Button -->
        <div class="row mt-5">
            <div class="col-12 text-center">
                <button class="btn btn-primary btn-lg px-4">
                    <i class="fas fa-plus me-2"></i>Load More Posts
                </button>
            </div>
        </div>
    </div>
</section>

<!-- Newsletter Section -->
<section class="py-5" style="background: #58E5F7;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 text-center">
                <h3 class="text-dark mb-3">Stay Updated</h3>
                <p class="text-dark mb-4">Get the latest game development insights and tutorials delivered to your inbox.</p>
                <form class="d-flex gap-2 justify-content-center">
                    <input type="email" class="form-control" placeholder="Enter your email" style="max-width: 300px;">
                    <button type="submit" class="btn btn-dark">Subscribe</button>
                </form>
            </div>
        </div>
    </div>
</section>


{% endblock %} 