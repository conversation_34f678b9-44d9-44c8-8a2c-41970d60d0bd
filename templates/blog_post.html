{% extends "base.html" %}

{% block title %}{{ post.title }} - Barbarossa Lives Game Studio{% endblock %}

{% block content %}
<!-- Blog Post Hero -->
<section class="py-5" style="background: linear-gradient(135deg, #653119 0%, #8B4513 100%);">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="/blog" class="text-white-50">Blog</a></li>
                        <li class="breadcrumb-item active text-white" aria-current="page">{{ post.category }}</li>
                    </ol>
                </nav>
                
                <div class="text-center">
                    <span class="badge bg-primary mb-3">{{ post.category }}</span>
                    <h1 class="display-5 fw-bold text-white mb-4">{{ post.title }}</h1>
                    
                    <div class="d-flex justify-content-center align-items-center text-white-50 mb-4">
                        <div class="d-flex align-items-center me-4">
                            <i class="fas fa-user me-2"></i>
                            <span>{{ post.author }}</span>
                        </div>
                        <div class="d-flex align-items-center me-4">
                            <i class="fas fa-calendar-alt me-2"></i>
                            <span>{{ post.date }}</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-clock me-2"></i>
                            <span>{{ post.read_time }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Blog Post Content -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Featured Image -->
                <div class="mb-5">
                    <img src="{{ url_for('static', path='/images/' + post.image) }}" 
                         class="img-fluid rounded shadow" 
                         alt="{{ post.title }}">
                </div>
                
                <!-- Article Content -->
                <article class="blog-content">
                    {{ post.content | safe }}
                </article>
                
                <!-- Tags -->
                {% if post.tags %}
                <div class="mt-5 pt-4 border-top">
                    <h6 class="mb-3">Tags:</h6>
                    <div class="d-flex flex-wrap gap-2">
                        {% for tag in post.tags %}
                        <span class="badge bg-secondary">{{ tag }}</span>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                
                <!-- Social Sharing -->
                <div class="mt-5 pt-4 border-top">
                    <h6 class="mb-3">Share this post:</h6>
                    <div class="d-flex gap-2">
                        <a href="https://twitter.com/intent/tweet?text={{ post.title | urlencode }}&url={{ request.url | urlencode }}" 
                           class="btn btn-outline-primary btn-sm" target="_blank">
                            <i class="fab fa-twitter me-1"></i>Twitter
                        </a>
                        <a href="https://www.facebook.com/sharer/sharer.php?u={{ request.url | urlencode }}" 
                           class="btn btn-outline-primary btn-sm" target="_blank">
                            <i class="fab fa-facebook me-1"></i>Facebook
                        </a>
                        <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ request.url | urlencode }}" 
                           class="btn btn-outline-primary btn-sm" target="_blank">
                            <i class="fab fa-linkedin me-1"></i>LinkedIn
                        </a>
                        <button class="btn btn-outline-secondary btn-sm" onclick="navigator.clipboard.writeText(window.location.href)">
                            <i class="fas fa-link me-1"></i>Copy Link
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="sticky-top" style="top: 100px;">
                    <!-- Author Info -->
                    <div class="card mb-4">
                        <div class="card-body text-center">
                            <img src="{{ url_for('static', path='/images/logoTransparentNoWords.png') }}" 
                                 alt="Author" 
                                 class="rounded-circle mb-3" 
                                 width="80" 
                                 height="80">
                            <h6 class="card-title">{{ post.author }}</h6>
                            <p class="card-text text-muted small">Game Developer & Studio Owner</p>
                            <a href="/about" class="btn btn-outline-primary btn-sm">Learn More</a>
                        </div>
                    </div>
                    
                    <!-- Table of Contents -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">Table of Contents</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <a href="#introduction" class="text-decoration-none">Introduction</a>
                                </li>
                                <li class="mb-2">
                                    <a href="#main-content" class="text-decoration-none">Main Content</a>
                                </li>
                                <li class="mb-2">
                                    <a href="#conclusion" class="text-decoration-none">Conclusion</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- Newsletter Signup -->
                    <div class="card" style="background: #58E5F7;">
                        <div class="card-body text-center">
                            <h6 class="text-dark">Stay Updated</h6>
                            <p class="text-dark small mb-3">Get the latest game development insights.</p>
                            <form>
                                <div class="mb-3">
                                    <input type="email" class="form-control form-control-sm" placeholder="Your email">
                                </div>
                                <button type="submit" class="btn btn-dark btn-sm w-100">Subscribe</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Posts -->
{% if related_posts %}
<section class="py-5 bg-light">
    <div class="container">
        <h3 class="mb-4">Related Posts</h3>
        <div class="row g-4">
            {% for related_post in related_posts %}
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <img src="{{ url_for('static', path='/images/' + related_post.image) }}" 
                         class="card-img-top" 
                         alt="{{ related_post.title }}"
                         style="height: 150px; object-fit: cover;">
                    <div class="card-body">
                        <span class="badge bg-primary mb-2">{{ related_post.category }}</span>
                        <h6 class="card-title">{{ related_post.title }}</h6>
                        <p class="card-text text-muted small">{{ related_post.excerpt[:100] }}...</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">{{ related_post.read_time }}</small>
                            <a href="/blog/{{ related_post.id }}" class="btn btn-outline-primary btn-sm">Read More</a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Comments Section -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <h3 class="mb-4">Comments</h3>
                <div class="card">
                    <div class="card-body">
                        <form>
                            <div class="mb-3">
                                <label for="comment" class="form-label">Leave a comment</label>
                                <textarea class="form-control" id="comment" rows="4" placeholder="Share your thoughts..."></textarea>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <input type="text" class="form-control" placeholder="Your name">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <input type="email" class="form-control" placeholder="Your email">
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">Post Comment</button>
                        </form>
                    </div>
                </div>
                
                <!-- Sample Comments -->
                <div class="mt-4">
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                                <h6 class="mb-0">John Doe</h6>
                                <small class="text-muted">2 days ago</small>
                            </div>
                            <p class="mb-0">Great article! I've been looking for resources to get started with game development. This is exactly what I needed.</p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                                <h6 class="mb-0">Jane Smith</h6>
                                <small class="text-muted">1 week ago</small>
                            </div>
                            <p class="mb-0">Thanks for sharing these insights. The cross-platform development tips are particularly helpful.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_css %}
<style>
.blog-content h2 {
    color: #653119;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.blog-content h3 {
    color: #8B4513;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
}

.blog-content p {
    line-height: 1.7;
    margin-bottom: 1rem;
}

.blog-content ul {
    margin-bottom: 1rem;
}

.blog-content li {
    margin-bottom: 0.5rem;
}
</style>
{% endblock %} 