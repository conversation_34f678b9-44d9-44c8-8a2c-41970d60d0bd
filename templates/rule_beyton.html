{% extends "base.html" %}

{% block title %}Rule Beyton - Game Project{% endblock %}

{% block content %}
<!-- Rule Beyton Hero Section -->
<section class="py-5" style="background: #653119;">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4 text-white">Rule Beyton</h1>
                <p class="lead text-white">
                    A 3D RPG game where you rise through the ranks of the thieves' guild to become the master.
                </p>
            </div>
            <div class="col-lg-6 text-center">
                <img src="{{ url_for('static', path='/images/RuleBeyton_1.png') }}" alt="Rule Beyton" style="width: 400px; height: 225px; object-fit: contain;">
            </div>
        </div>
    </div>
</section>

<!-- Project Description Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <h2 class="fw-bold mb-4">About the Game</h2>
                <p class="lead mb-4">
                    Rule Beyton is an ambitious 3D RPG project in development by Barbarossa Lives Game Studio. Set in a gritty, living city, you start as a lowly thief and work your way up the criminal underworld. The game blends classic role-playing mechanics with modern AI-driven systems for dynamic encounters and emergent gameplay.
                </p>
                <h3 class="fw-bold mt-5 mb-3">Key Features</h3>
                <ul class="list-unstyled mb-4">
                    <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i>Open-world city with dynamic NPCs and factions</li>
                    <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i>Stealth, combat, and social manipulation gameplay</li>
                    <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i>Progression from street thief to guild master</li>
                    <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i>AI-powered quest and event generation</li>
                    <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i>Rich narrative and player-driven choices</li>
                </ul>
                <h3 class="fw-bold mt-5 mb-3">Development Status</h3>
                <p>
                    Rule Beyton is currently in early development. Follow our progress for updates, devlogs, and opportunities to get involved in playtesting and feedback.
                </p>
                <div class="mt-4 mb-5">
                    <a href="https://github.com/BarbarossaLives/RuleBeyton" target="_blank" class="btn btn-outline-secondary me-2"><i class="fab fa-github me-1"></i>GitHub Repo</a>
                    <a href="/games" class="btn btn-outline-dark me-2"><i class="fas fa-arrow-left me-1"></i>Back to Games</a>
                    <a href="/contact" class="btn btn-warning">Contact Us</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Gallery Section -->
<section class="py-5 bg-dark">
    <div class="container">
        <div class="row text-center mb-4">
            <div class="col-lg-8 mx-auto">
                <h2 class="display-6 fw-bold mb-3 text-white">Gallery</h2>
                <p class="lead text-light mb-4">Screenshots and concept art from Rule Beyton.</p>
            </div>
        </div>
        <div id="ruleBeytonGallery" class="carousel slide" data-bs-ride="carousel">
            <div class="carousel-indicators">
                <button type="button" data-bs-target="#ruleBeytonGallery" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
                <button type="button" data-bs-target="#ruleBeytonGallery" data-bs-slide-to="1" aria-label="Slide 2"></button>
                <button type="button" data-bs-target="#ruleBeytonGallery" data-bs-slide-to="2" aria-label="Slide 3"></button>
            </div>
            <div class="carousel-inner">
                <div class="carousel-item active">
                    <img src="{{ url_for('static', path='/images/RuleBeyton_1.png') }}" class="d-block w-100" alt="Rule Beyton Screenshot 1" style="max-height: 500px; object-fit: contain;">
                </div>
                
            </div>
            <button class="carousel-control-prev" type="button" data-bs-target="#ruleBeytonGallery" data-bs-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Previous</span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#ruleBeytonGallery" data-bs-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Next</span>
            </button>
        </div>
    </div>
</section>
{% endblock %} 