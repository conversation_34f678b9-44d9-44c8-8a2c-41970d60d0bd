{% extends "base.html" %}

{% block title %}Admin Dashboard - Barbarossa Lives Game Studio{% endblock %}

{% block content %}
<!-- Admin Header -->
<section class="py-4" style="background: linear-gradient(135deg, #653119 0%, #8B4513 100%);">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="h2 text-white mb-0">Admin Dashboard</h1>
                <p class="text-white-50 mb-0">Manage your website content and settings</p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="btn-group" role="group">
                    <a href="/" class="btn btn-light">
                        <i class="fas fa-home me-2"></i>View Site
                    </a>
                    <a href="/admin/logout" class="btn btn-outline-light">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Quick Stats -->
<section class="py-4">
    <div class="container">
        <div class="row g-4">
            <div class="col-md-3">
                <div class="card text-center border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fas fa-newspaper fa-2x text-primary mb-3"></i>
                        <h3 class="mb-1">{{ blog_posts|length }}</h3>
                        <p class="text-muted mb-2">Blog Posts</p>
                        <a href="/admin/blog" class="btn btn-outline-primary btn-sm">Manage Posts</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fas fa-check-circle fa-2x text-success mb-3"></i>
                        <h3 class="mb-1">{{ blog_posts|selectattr('published', 'equalto', true)|list|length }}</h3>
                        <p class="text-muted mb-2">Published</p>
                        <a href="/blog" class="btn btn-outline-success btn-sm">View Blog</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fas fa-clock fa-2x text-warning mb-3"></i>
                        <h3 class="mb-1">{{ blog_posts|selectattr('published', 'equalto', false)|list|length }}</h3>
                        <p class="text-muted mb-2">Drafts</p>
                        <a href="/admin/blog/new" class="btn btn-outline-warning btn-sm">Create Post</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fas fa-tags fa-2x text-info mb-3"></i>
                        <h3 class="mb-1">{{ categories|length }}</h3>
                        <p class="text-muted mb-2">Categories</p>
                        <a href="/admin/blog" class="btn btn-outline-info btn-sm">Manage</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Quick Actions -->
<section class="py-4 bg-light">
    <div class="container">
        <h3 class="mb-4">Quick Actions</h3>
        <div class="row g-4">
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-plus-circle text-primary me-2"></i>
                            Create New Blog Post
                        </h5>
                        <p class="card-text">Write and publish a new blog post about game development, industry insights, or your latest projects.</p>
                        <a href="/admin/blog/new" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>New Post
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-edit text-secondary me-2"></i>
                            Manage Blog Posts
                        </h5>
                        <p class="card-text">Edit existing posts, manage drafts, and organize your blog content with categories and tags.</p>
                        <a href="/admin/blog" class="btn btn-secondary">
                            <i class="fas fa-cog me-2"></i>Manage Posts
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Recent Posts -->
<section class="py-4">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h3>Recent Blog Posts</h3>
            <a href="/admin/blog" class="btn btn-outline-primary">View All</a>
        </div>
        
        {% if blog_posts %}
        <div class="row g-4">
            {% for post in blog_posts[:3] %}
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <img src="{{ url_for('static', path='/images/' + post.image) }}" 
                         class="card-img-top" 
                         alt="{{ post.title }}"
                         style="height: 150px; object-fit: cover;">
                    <div class="card-body">
                        <span class="badge bg-primary mb-2">{{ post.category }}</span>
                        <h6 class="card-title">{{ post.title }}</h6>
                        <p class="card-text text-muted small">{{ post.excerpt[:80] }}...</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">{{ post.date }}</small>
                            <div class="btn-group btn-group-sm">
                                <a href="/blog/{{ post.id }}" class="btn btn-outline-primary" target="_blank">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="/admin/blog/{{ post.id }}/edit" class="btn btn-outline-secondary">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No blog posts yet</h5>
            <p class="text-muted">Create your first blog post to get started!</p>
            <a href="/admin/blog/new" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Create First Post
            </a>
        </div>
        {% endif %}
    </div>
</section>

<!-- System Info -->
<section class="py-4 bg-light">
    <div class="container">
        <h3 class="mb-4">System Information</h3>
        <div class="row g-4">
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">Website Statistics</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2">
                                <strong>Total Posts:</strong> {{ blog_posts|length }}
                            </li>
                            <li class="mb-2">
                                <strong>Published:</strong> {{ blog_posts|selectattr('published', 'equalto', true)|list|length }}
                            </li>
                            <li class="mb-2">
                                <strong>Drafts:</strong> {{ blog_posts|selectattr('published', 'equalto', false)|list|length }}
                            </li>
                            <li class="mb-2">
                                <strong>Categories:</strong> {{ categories|length }}
                            </li>
                            <li class="mb-2">
                                <strong>Tags:</strong> {{ tags|length }}
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">Quick Links</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="/" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-home me-2"></i>View Homepage
                            </a>
                            <a href="/blog" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-newspaper me-2"></i>View Blog
                            </a>
                            <a href="/about" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-info-circle me-2"></i>About Page
                            </a>
                            <a href="/contact" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-envelope me-2"></i>Contact Page
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %} 