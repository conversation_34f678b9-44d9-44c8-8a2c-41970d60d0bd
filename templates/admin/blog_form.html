{% extends "base.html" %}

{% block title %}{% if post %}Edit Post{% else %}New Post{% endif %} - Blog Admin{% endblock %}

{% block content %}
<!-- Admin Header -->
<section class="py-4" style="background: linear-gradient(135deg, #653119 0%, #8B4513 100%);">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="h2 text-white mb-0">
                    {% if post %}Edit Post{% else %}Create New Post{% endif %}
                </h1>
                <p class="text-white-50 mb-0">
                    {% if post %}Update "{{ post.title }}"{% else %}Write your next blog post{% endif %}
                </p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="btn-group" role="group">
                    <a href="/admin/blog" class="btn btn-light">
                        <i class="fas fa-arrow-left me-2"></i>Back to Posts
                    </a>
                    <a href="/admin/logout" class="btn btn-outline-light">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Blog Post Form -->
<section class="py-4">
    <div class="container">
        <form method="POST" action="{% if post %}/admin/blog/{{ post.id }}/edit{% else %}/admin/blog/new{% endif %}">
            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-8">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Post Content</h5>
                        </div>
                        <div class="card-body">
                            <!-- Title -->
                            <div class="mb-3">
                                <label for="title" class="form-label">Post Title *</label>
                                <input type="text" class="form-control form-control-lg" id="title" name="title" 
                                       value="{{ post.title if post else '' }}" required>
                                <div class="form-text">This will be the main heading of your post</div>
                            </div>

                            <!-- Excerpt -->
                            <div class="mb-3">
                                <label for="excerpt" class="form-label">Excerpt *</label>
                                <textarea class="form-control" id="excerpt" name="excerpt" rows="3" required>{{ post.excerpt if post else '' }}</textarea>
                                <div class="form-text">Brief description shown in blog listings (max 200 characters)</div>
                            </div>

                            <!-- Content -->
                            <div class="mb-3">
                                <label for="content" class="form-label">Content *</label>
                                <div class="border rounded p-2 mb-2 bg-light">
                                    <small class="text-muted">
                                        <strong>HTML Formatting Tips:</strong><br>
                                        • Use &lt;h2&gt; and &lt;h3&gt; for headers<br>
                                        • Use &lt;p&gt; for paragraphs<br>
                                        • Use &lt;ul&gt; and &lt;li&gt; for lists<br>
                                        • Use &lt;strong&gt; for bold text<br>
                                        • Use &lt;em&gt; for italic text<br>
                                        • Use &lt;a href="..."&gt; for links
                                    </small>
                                </div>
                                <textarea class="form-control" id="content" name="content" rows="15" required>{{ post.content if post else '' }}</textarea>
                                <div class="form-text">Write your post content using HTML formatting</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Publish Settings -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">Publish Settings</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="published" name="published" 
                                           {% if not post or post.published %}checked{% endif %}>
                                    <label class="form-check-label" for="published">
                                        Publish immediately
                                    </label>
                                </div>
                                <div class="form-text">Uncheck to save as draft</div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    {% if post %}Update Post{% else %}Create Post{% endif %}
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Post Settings -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">Post Settings</h6>
                        </div>
                        <div class="card-body">
                            <!-- Category -->
                            <div class="mb-3">
                                <label for="category" class="form-label">Category *</label>
                                <select class="form-select" id="category" name="category" required>
                                    <option value="">Choose a category...</option>
                                    {% for cat in categories %}
                                    <option value="{{ cat.name }}" {% if post and post.category == cat.name %}selected{% endif %}>
                                        {{ cat.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- Image -->
                            <div class="mb-3">
                                <label for="image" class="form-label">Featured Image</label>
                                <input type="text" class="form-control" id="image" name="image" 
                                       value="{{ post.image if post else 'game_tool_full.png' }}">
                                <div class="form-text">Filename from static/images/ folder</div>
                            </div>

                            <!-- Read Time -->
                            <div class="mb-3">
                                <label for="read_time" class="form-label">Estimated Read Time</label>
                                <input type="text" class="form-control" id="read_time" name="read_time" 
                                       value="{{ post.read_time if post else '5 min read' }}">
                                <div class="form-text">e.g., "5 min read", "10 min read"</div>
                            </div>

                            <!-- Tags -->
                            <div class="mb-3">
                                <label for="tags" class="form-label">Tags</label>
                                <input type="text" class="form-control" id="tags" name="tags" 
                                       value="{{ post.tags|join(', ') if post else '' }}">
                                <div class="form-text">Comma-separated tags (e.g., beginner, tutorial, unity)</div>
                            </div>
                        </div>
                    </div>

                    <!-- Available Images -->
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">Available Images</h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="border rounded p-2 text-center">
                                        <img src="{{ url_for('static', path='/images/game_tool_full.png') }}" 
                                             class="img-fluid mb-1" style="height: 60px; object-fit: cover;">
                                        <small class="d-block">game_tool_full.png</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2 text-center">
                                        <img src="{{ url_for('static', path='/images/CryptoSim.png') }}" 
                                             class="img-fluid mb-1" style="height: 60px; object-fit: cover;">
                                        <small class="d-block">CryptoSim.png</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2 text-center">
                                        <img src="{{ url_for('static', path='/images/CrossPostingApp.png') }}" 
                                             class="img-fluid mb-1" style="height: 60px; object-fit: cover;">
                                        <small class="d-block">CrossPostingApp.png</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="border rounded p-2 text-center">
                                        <img src="{{ url_for('static', path='/images/logoTransparentNoWords.png') }}" 
                                             class="img-fluid mb-1" style="height: 60px; object-fit: cover;">
                                        <small class="d-block">logoTransparentNoWords.png</small>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">Click on an image to use it as featured image</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
// Auto-save functionality
let autoSaveTimer;
const form = document.querySelector('form');
const inputs = form.querySelectorAll('input, textarea, select');

inputs.forEach(input => {
    input.addEventListener('input', () => {
        clearTimeout(autoSaveTimer);
        autoSaveTimer = setTimeout(() => {
            // Show auto-save indicator
            const saveBtn = document.querySelector('button[type="submit"]');
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '<i class="fas fa-save me-2"></i>Auto-saving...';
            saveBtn.disabled = true;
            
            setTimeout(() => {
                saveBtn.innerHTML = originalText;
                saveBtn.disabled = false;
            }, 1000);
        }, 2000);
    });
});

// Image selection
document.querySelectorAll('.card-body img').forEach(img => {
    img.addEventListener('click', () => {
        const filename = img.nextElementSibling.textContent;
        document.getElementById('image').value = filename;
        
        // Highlight selected image
        document.querySelectorAll('.card-body .border').forEach(border => {
            border.classList.remove('border-primary');
        });
        img.parentElement.classList.add('border-primary');
    });
});

// Character counter for excerpt
const excerptTextarea = document.getElementById('excerpt');
excerptTextarea.addEventListener('input', () => {
    const maxLength = 200;
    const currentLength = excerptTextarea.value.length;
    const remaining = maxLength - currentLength;
    
    let counter = excerptTextarea.parentElement.querySelector('.char-counter');
    if (!counter) {
        counter = document.createElement('small');
        counter.className = 'char-counter text-muted';
        excerptTextarea.parentElement.appendChild(counter);
    }
    
    counter.textContent = `${remaining} characters remaining`;
    
    if (remaining < 0) {
        counter.classList.add('text-danger');
    } else {
        counter.classList.remove('text-danger');
    }
});

// Form validation
form.addEventListener('submit', (e) => {
    const title = document.getElementById('title').value.trim();
    const excerpt = document.getElementById('excerpt').value.trim();
    const content = document.getElementById('content').value.trim();
    const category = document.getElementById('category').value;
    
    if (!title || !excerpt || !content || !category) {
        e.preventDefault();
        alert('Please fill in all required fields (marked with *)');
        return;
    }
    
    if (excerpt.length > 200) {
        e.preventDefault();
        alert('Excerpt must be 200 characters or less');
        return;
    }
});
</script>
{% endblock %} 