/* Custom Styles for Business Website */

/* CSS Custom Properties */
:root {
    --xanthous: #FFBF41;
    --seal-brown: #653119;
    --safety-orange: #FE770E;
    --honolulu-blue: #0275B7;
    --electric-blue: #58E5F7;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;

}

/* Logo Styling */
.navbar-brand img {
    height: 40px;
    width: auto;
    transition: all 0.3s ease;
    filter: brightness(1);
}

.navbar-brand:hover img {
    filter: brightness(1.2);
    transform: scale(1.05);
}

/* Hero Section */
.hero-section {
    background: #653119;
    overflow: hidden;
    margin-top: 20px; /* Additional spacing from navbar */
}

.hero-image {
    position: relative;
    z-index: 2;
}

/* Cards */
.card {
    transition: all 0.3s ease;
    border-radius: 15px;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
}

/* Buttons */
.btn {
    transition: all 0.3s ease;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Service Icons */
.service-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    font-size: 2rem;
    transition: all 0.3s ease;
}

.service-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 10px 25px rgba(0, 123, 255, 0.3);
}

/* Navigation */
.navbar {
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.nav-link {
    position: relative;
    transition: all 0.3s ease;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background-color: #ffc107;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::after {
    width: 100%;
}

/* Feature Items */
.feature-item {
    transition: all 0.3s ease;
    padding: 1rem;
    border-radius: 10px;
}

.feature-item:hover {
    background-color: #f8f9fa;
    transform: translateX(10px);
}

.feature-icon {
    transition: all 0.3s ease;
}

.feature-item:hover .feature-icon {
    transform: scale(1.2);
}

/* Social Links */
.social-links a {
    transition: all 0.3s ease;
    display: inline-block;
}

.social-links a:hover {
    transform: translateY(-3px);
    color: #ffc107 !important;
}

/* Footer */
footer {
    margin-top: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 2rem 0;
    }
    
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .btn {
        margin-bottom: 1rem;
    }
    
    .feature-item:hover {
        transform: none;
    }
    
    .navbar-brand img {
        height: 30px !important;
    }
    
    .display-4 {
        font-size: 2.5rem;
    }
    
    .display-5 {
        font-size: 2rem;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    display: none !important;
}

/* Hide scrollbar for Firefox */
html {
    scrollbar-width: none !important;
}

/* Hide scrollbar for IE/Edge */
body {
    -ms-overflow-style: none !important;
}

/* Text Selection */
::selection {
    background-color: #ffc107;
    color: #000;
}

/* Focus States */
.btn:focus,
.nav-link:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    footer {
        display: none !important;
    }
}

/* Dark Mode Styles */
body.dark-mode {
    background-color: #181a1b !important;
    color: #f1f1f1 !important;
}
body.dark-mode .navbar,
body.dark-mode footer {
    background-color: #23272b !important;
    color: #f1f1f1 !important;
}
body.dark-mode .navbar .navbar-brand,
body.dark-mode .navbar .nav-link {
    color: #f1f1f1 !important;
}
body.dark-mode .navbar .nav-link.active,
body.dark-mode .navbar .nav-link:hover {
    color: #58E5F7 !important;
}
body.dark-mode .card {
    background-color: #23272b !important;
    color: #f1f1f1 !important;
    border-color: #23272b !important;
}
body.dark-mode .card-title,
body.dark-mode .card-text,
body.dark-mode .text-dark {
    color: #f1f1f1 !important;
}
body.dark-mode .btn {
    background-color: #23272b;
    color: #f1f1f1;
    border-color: #58E5F7;
}
body.dark-mode .btn:hover {
    background-color: #58E5F7;
    color: #23272b;
    border-color: #58E5F7;
}
body.dark-mode .bg-dark {
    background-color: #181a1b !important;
}
body.dark-mode .bg-light {
    background-color: #23272b !important;
    color: #f1f1f1 !important;
}
body.dark-mode .text-dark {
    color: #f1f1f1 !important;
}
body.dark-mode .form-control,
body.dark-mode .form-select {
    background-color: #23272b !important;
    color: #f1f1f1 !important;
    border-color: #444 !important;
}
body.dark-mode input,
body.dark-mode textarea {
    background-color: #23272b !important;
    color: #f1f1f1 !important;
}
body.dark-mode .badge.bg-primary {
    background-color: #58E5F7 !important;
    color: #23272b !important;
}
body.dark-mode .badge.bg-success {
    background-color: #27ae60 !important;
    color: #fff !important;
}
body.dark-mode .badge.bg-warning {
    background-color: #ffc107 !important;
    color: #23272b !important;
}
body.dark-mode .badge.bg-secondary {
    background-color: #888 !important;
    color: #23272b !important;
}
body.dark-mode .social-links a {
    color: #f1f1f1 !important;
}
body.dark-mode .social-links a:hover {
    color: #58E5F7 !important;
} 